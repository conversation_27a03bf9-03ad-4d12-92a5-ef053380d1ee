/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* External Highlight Styles */

.external-highlight-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.external-highlight {
  position: absolute;
  pointer-events: none;
  z-index: 1;
  border-radius: 2px;
  transition: opacity 0.2s ease-in-out;

  /* Default highlight style */
  background-color: rgba(245, 34, 45, 0.2);
  border: 1px solid rgba(245, 34, 45, 1);
}

.external-highlight:hover {
  opacity: 0.8;
}

/* Text highlight styles - 绿色高亮背景 */
.external-highlight.highlight-with-text {
  background-color: #F6FFED !important;
  border: 2px solid #52C41A !important;
  opacity: 1 !important;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3) !important;
}

/* Text element styles - 文本显示样式 */
.external-highlight-text {
  background-color: #52C41A;
  color: #FFFFFF;
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 12px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 4px;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.4;
  max-height: 120px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #52C41A;
  pointer-events: none;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 文本截断提示 */
.external-highlight-text .ellipsis {
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
}

/* Different highlight color variants */
.external-highlight.highlight-yellow {
  background-color: rgba(255, 255, 0, 0.3);
  border-color: rgba(255, 255, 0, 0.8);
}

.external-highlight.highlight-green {
  background-color: rgba(0, 255, 0, 0.3);
  border-color: rgba(0, 255, 0, 0.8);
}

.external-highlight.highlight-blue {
  background-color: rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 150, 255, 0.8);
}

.external-highlight.highlight-red {
  background-color: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.8);
}

.external-highlight.highlight-orange {
  background-color: rgba(255, 165, 0, 0.3);
  border-color: rgba(255, 165, 0, 0.8);
}

.external-highlight.highlight-purple {
  background-color: rgba(128, 0, 128, 0.3);
  border-color: rgba(128, 0, 128, 0.8);
}

.external-highlight.highlight-pink {
  background-color: rgba(255, 192, 203, 0.3);
  border-color: rgba(255, 192, 203, 0.8);
}

/* Highlight with custom opacity */
.external-highlight.highlight-light {
  background-color: rgba(255, 255, 0, 0.2);
  border-color: rgba(255, 255, 0, 0.6);
}

.external-highlight.highlight-medium {
  background-color: rgba(255, 255, 0, 0.4);
  border-color: rgba(255, 255, 0, 0.9);
}

.external-highlight.highlight-strong {
  background-color: rgba(255, 255, 0, 0.6);
  border-color: rgba(255, 255, 0, 1.0);
}

/* Highlight with different border styles */
.external-highlight.highlight-dashed {
  border-style: dashed;
}

.external-highlight.highlight-dotted {
  border-style: dotted;
}

.external-highlight.highlight-thick {
  border-width: 2px;
}

.external-highlight.highlight-thin {
  border-width: 0.5px;
}

/* Animation effects */
.external-highlight.highlight-pulse {
  animation: highlightPulse 2s infinite;
}

@keyframes highlightPulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.3;
  }
}

.external-highlight.highlight-fade-in {
  animation: highlightFadeIn 1s ease-in-out;
}

@keyframes highlightFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0.3;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .external-highlight {
    border-width: 1.5px;
  }
}

@media (max-width: 480px) {
  .external-highlight {
    border-width: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .external-highlight {
    background-color: rgba(255, 255, 0, 0.5);
    border-color: rgba(255, 255, 0, 1.0);
    border-width: 2px;
  }

  .external-highlight.highlight-with-text {
    background-color: rgba(246, 255, 237, 1) !important;
    border: 2px solid rgba(0, 128, 0, 1) !important;
  }

  .external-highlight-text {
    background-color: rgba(246, 255, 237, 1);
    color: #000000;
    border: 2px solid rgba(0, 128, 0, 1);
    font-weight: bold;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .external-highlight {
    background-color: rgba(255, 255, 100, 0.4);
    border-color: rgba(255, 255, 100, 0.9);
  }

  .external-highlight.highlight-yellow {
    background-color: rgba(255, 255, 100, 0.4);
    border-color: rgba(255, 255, 100, 0.9);
  }

  .external-highlight.highlight-green {
    background-color: rgba(100, 255, 100, 0.4);
    border-color: rgba(100, 255, 100, 0.9);
  }

  .external-highlight.highlight-blue {
    background-color: rgba(100, 200, 255, 0.4);
    border-color: rgba(100, 200, 255, 0.9);
  }

  /* Text highlight styles in dark mode - keep light green for visibility */
  .external-highlight.highlight-with-text {
    background-color: rgba(246, 255, 237, 1) !important;
    border: 1px solid rgba(246, 255, 237, 1) !important;
  }

  .external-highlight-text {
    background-color: rgba(246, 255, 237, 1);
    color: #000000;
    border: 1px solid rgba(246, 255, 237, 1);
  }
}

/* Print styles */
@media print {
  .external-highlight {
    background-color: rgba(255, 255, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 0, 0.6) !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .external-highlight.highlight-with-text {
    background-color: rgba(144, 238, 144, 0.8) !important;
    border: 1px solid rgba(246, 255, 237, 1) !important;
  }

  .external-highlight-text {
    background-color: rgba(144, 238, 144, 0.8) !important;
    color: #000000 !important;
    border: 1px solid rgba(246, 255, 237, 1) !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

/* Focus and accessibility */
.external-highlight[tabindex] {
  pointer-events: auto;
}

.external-highlight[tabindex]:focus {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

/* Tooltip support */
.external-highlight[title] {
  cursor: help;
  pointer-events: auto;
}

.external-highlight[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}
