<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js 高亮文本功能示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .example-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight-demo {
            position: relative;
            display: inline-block;
            margin: 10px;
        }
        .demo-highlight {
            background-color: rgba(144, 238, 144, 1);
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid rgba(144, 238, 144, 1);
        }
        .demo-text {
            position: absolute;
            bottom: 100%;
            left: 0;
            margin-bottom: 2px;
            background-color: rgba(144, 238, 144, 1);
            color: #000000;
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(144, 238, 144, 1);
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <h1>PDF.js 高亮文本功能示例</h1>
    
    <div class="example-section">
        <h2>功能说明</h2>
        <p>基于PDF.js的高亮选区功能，新增了文本显示功能：</p>
        <ul>
            <li><strong>有文本时</strong>：显示不透明的浅绿色背景，文本显示在选区上方，使用黑色微软雅黑字体</li>
            <li><strong>无文本时</strong>：保持原有的默认高亮样式</li>
        </ul>
    </div>

    <div class="example-section">
        <h2>视觉效果演示</h2>
        <div class="highlight-demo">
            <div class="demo-text">这是高亮文本</div>
            <div class="demo-highlight">高亮选区内容</div>
        </div>
        <div class="highlight-demo">
            <div class="demo-text">重要注释</div>
            <div class="demo-highlight">另一个高亮区域</div>
        </div>
    </div>

    <div class="example-section">
        <h2>URL参数使用方法</h2>
        <p>在PDF.js的URL中添加highlights参数，支持text字段：</p>
        <div class="code-block">
<pre>
// 带文本的高亮
?highlights=[
  {
    "page": 1,
    "x1": 100,
    "y1": 200,
    "x2": 400,
    "y2": 250,
    "text": "这是高亮文本注释"
  }
]

// 不带文本的高亮（使用默认样式）
?highlights=[
  {
    "page": 1,
    "x1": 100,
    "y1": 300,
    "x2": 400,
    "y2": 350
  }
]
</pre>
        </div>
    </div>

    <div class="example-section">
        <h2>JavaScript API使用方法</h2>
        <div class="code-block">
<pre>
// 创建外部高亮管理器实例
const highlightManager = new ExternalHighlightManager();

// 添加带文本的高亮
highlightManager.addHighlights([
  {
    page: 1,
    x1: 100,
    y1: 200,
    x2: 400,
    y2: 250,
    text: "重要内容标注"
  },
  {
    page: 1,
    x1: 100,
    y1: 300,
    x2: 400,
    y2: 350
    // 没有text字段，使用默认样式
  }
]);
</pre>
        </div>
    </div>

    <div class="example-section">
        <h2>样式特性</h2>
        <ul>
            <li><strong>背景色</strong>：rgba(144, 238, 144, 1) - 不透明浅绿色</li>
            <li><strong>字体</strong>：Microsoft YaHei (微软雅黑)</li>
            <li><strong>字体颜色</strong>：#000000 (黑色)</li>
            <li><strong>文本位置</strong>：显示在高亮选区上方</li>
            <li><strong>响应式</strong>：支持深色模式、高对比度模式和打印样式</li>
            <li><strong>文本处理</strong>：超长文本自动省略显示</li>
        </ul>
    </div>

    <div class="example-section">
        <h2>兼容性说明</h2>
        <ul>
            <li>完全兼容现有的高亮功能</li>
            <li>向后兼容，不影响已有的高亮数据</li>
            <li>支持所有现代浏览器</li>
            <li>支持移动端设备</li>
        </ul>
    </div>
</body>
</html>
