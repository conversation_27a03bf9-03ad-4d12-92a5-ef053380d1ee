<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js 高亮文本功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            margin: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .url-box {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .highlight-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .preview-item {
            position: relative;
            display: inline-block;
            margin: 10px;
        }
        .demo-highlight {
            background-color: #F6FFED;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #B7EB8F;
            min-width: 150px;
            text-align: center;
        }
        .demo-highlight-default {
            background-color: rgba(255, 255, 0, 0.3);
            border: 1px solid rgba(255, 255, 0, 0.8);
            padding: 8px 12px;
            border-radius: 4px;
            min-width: 150px;
            text-align: center;
        }
        .demo-text {
            position: absolute;
            bottom: 100%;
            left: 0;
            margin-bottom: 2px;
            background-color: #B7EB8F;
            color: #000000;
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.4;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 演示区域的高亮样式 */
        .demo-highlight-element {
            position: absolute;
            background-color: #F6FFED;
            border: 1px solid #B7EB8F;
            border-radius: 2px;
            pointer-events: none;
            z-index: 1;
        }

        .demo-highlight-element.default {
            background-color: rgba(255, 255, 0, 0.3);
            border: 1px solid rgba(255, 255, 0, 0.8);
        }

        .demo-text-element {
            position: absolute;
            background-color: #B7EB8F;
            color: #000000;
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.4;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            pointer-events: none;
            z-index: 2;
        }
        .test-data {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF.js 高亮文本功能测试</h1>
        <p>使用测试PDF文件：<strong>[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf</strong></p>

        <div class="test-section">
            <h2>功能预览</h2>
            <div class="highlight-preview">
                <div class="preview-item">
                    <div class="demo-text">电化学测试仪</div>
                    <div class="demo-highlight">带文本的高亮</div>
                </div>
                <div class="preview-item">
                    <div class="demo-text">重要采购信息</div>
                    <div class="demo-highlight">采购相关内容</div>
                </div>
                <div class="preview-item">
                    <div class="demo-highlight-default">默认高亮样式</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>实时演示</h2>
            <p>在下方的模拟PDF区域中查看高亮效果：</p>
            <div id="demo-container" style="position: relative; width: 100%; height: 400px; border: 2px solid #ddd; border-radius: 8px; background: white; overflow: hidden;">
                <div id="demo-page" style="position: relative; width: 100%; height: 100%; background: #fafafa; padding: 20px;">
                    <h3 style="margin: 0 0 20px 0; color: #333;">深圳大学电化学测试仪采购文档</h3>
                    <p style="margin: 10px 0; line-height: 1.6;">本次采购项目为深圳大学化学与环境工程学院电化学测试仪设备采购。</p>
                    <p style="margin: 10px 0; line-height: 1.6;">设备要求具备高精度测量能力，支持多种电化学测试方法。</p>
                    <p style="margin: 10px 0; line-height: 1.6;">采购预算范围在合理区间内，符合学校采购管理规定。</p>
                    <p style="margin: 10px 0; line-height: 1.6;">供应商需提供完整的技术方案和售后服务保障。</p>

                    <!-- 高亮演示区域 -->
                    <div id="highlight-demo-layer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 10;"></div>
                </div>
            </div>

            <div style="margin-top: 20px;">
                <button class="button" onclick="showDemo1()">演示1: 基础功能</button>
                <button class="button" onclick="showDemo2()">演示2: 长文本换行</button>
                <button class="button" onclick="showDemo3()">演示3: 混合样式</button>
                <button class="button" onclick="clearDemo()">清除演示</button>
            </div>
        </div>

        <div class="test-section">
            <h2>测试用例 1：基础功能测试</h2>
            <p>测试带文本和不带文本的高亮显示效果</p>
            <div class="test-data">
                <strong>测试数据：</strong>
                <pre id="testData1"></pre>
            </div>
            <div class="url-box" id="testUrl1"></div>
            <a href="#" class="button" id="openTest1">在新窗口中打开测试</a>
            <button class="button" onclick="copyToClipboard('testUrl1')">复制URL</button>
        </div>

        <div class="test-section">
            <h2>测试用例 2：多页面高亮测试</h2>
            <p>测试在不同页面上的高亮效果</p>
            <div class="test-data">
                <strong>测试数据：</strong>
                <pre id="testData2"></pre>
            </div>
            <div class="url-box" id="testUrl2"></div>
            <a href="#" class="button" id="openTest2">在新窗口中打开测试</a>
            <button class="button" onclick="copyToClipboard('testUrl2')">复制URL</button>
        </div>

        <div class="test-section">
            <h2>测试用例 3：长文本截断测试</h2>
            <p>测试超长文本的截断显示效果</p>
            <div class="test-data">
                <strong>测试数据：</strong>
                <pre id="testData3"></pre>
            </div>
            <div class="url-box" id="testUrl3"></div>
            <a href="#" class="button" id="openTest3">在新窗口中打开测试</a>
            <button class="button" onclick="copyToClipboard('testUrl3')">复制URL</button>
        </div>

        <div class="test-section">
            <h2>使用说明</h2>
            <div class="status info">
                <strong>注意：</strong>请确保PDF文件 <code>[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf</code> 
                已放置在正确的位置，并且PDF.js viewer可以访问到该文件。
            </div>
            <ol>
                <li>点击上方的"在新窗口中打开测试"按钮</li>
                <li>等待PDF加载完成</li>
                <li>观察高亮效果：
                    <ul>
                        <li>带文本的高亮应该显示浅绿色背景和上方的文本标注</li>
                        <li>不带文本的高亮应该显示默认的黄色半透明背景</li>
                    </ul>
                </li>
                <li>可以尝试缩放、翻页等操作验证高亮的响应性</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>验证清单</h2>
            <div class="status success">
                <strong>验证要点：</strong>
                <ul>
                    <li>✅ 带文本的高亮显示浅绿色不透明背景</li>
                    <li>✅ 文本显示在高亮区域上方</li>
                    <li>✅ 文本使用黑色微软雅黑字体</li>
                    <li>✅ 不带文本的高亮使用默认样式</li>
                    <li>✅ 长文本正确截断显示</li>
                    <li>✅ 高亮位置准确对应PDF内容</li>
                    <li>✅ 缩放时高亮位置正确调整</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData1 = [
            {
                page: 1,
                x1: 100,
                y1: 700,
                x2: 300,
                y2: 720,
                text: "电化学测试仪",
                id: "highlight-1"
            },
            {
                page: 1,
                x1: 100,
                y1: 650,
                x2: 250,
                y2: 670,
                text: "采购信息",
                id: "highlight-2"
            },
            {
                page: 1,
                x1: 100,
                y1: 600,
                x2: 200,
                y2: 620,
                id: "highlight-3"
            }
        ];

        const testData2 = [
            {
                page: 1,
                x1: 50,
                y1: 500,
                x2: 400,
                y2: 520,
                text: "第一页重要内容",
                id: "page1-highlight-1"
            },
            {
                page: 2,
                x1: 50,
                y1: 700,
                x2: 350,
                y2: 720,
                text: "第二页标注",
                id: "page2-highlight-1"
            },
            {
                page: 1,
                x1: 50,
                y1: 450,
                x2: 300,
                y2: 470,
                id: "page1-highlight-2"
            }
        ];

        const testData3 = [
            {
                page: 1,
                x1: 100,
                y1: 400,
                x2: 400,
                y2: 420,
                text: "这是一个非常长的文本注释，用来测试文本截断功能是否能够正常工作，当文本超过容器宽度时应该显示省略号",
                id: "long-text-highlight"
            },
            {
                page: 1,
                x1: 100,
                y1: 350,
                x2: 200,
                y2: 370,
                text: "短文本",
                id: "short-text-highlight"
            }
        ];

        // 生成测试URL
        function generateTestURL(testData, pdfFile = '[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf') {
            const baseUrl = 'viewer.html';
            const highlightsParam = encodeURIComponent(JSON.stringify(testData));
            const fileParam = encodeURIComponent(pdfFile);
            return `${baseUrl}?file=${fileParam}&highlights=${highlightsParam}`;
        }

        // 复制到剪贴板
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('URL已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('URL已复制到剪贴板');
            });
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 设置测试数据显示
            document.getElementById('testData1').textContent = JSON.stringify(testData1, null, 2);
            document.getElementById('testData2').textContent = JSON.stringify(testData2, null, 2);
            document.getElementById('testData3').textContent = JSON.stringify(testData3, null, 2);

            // 生成测试URL
            const testUrl1 = generateTestURL(testData1);
            const testUrl2 = generateTestURL(testData2);
            const testUrl3 = generateTestURL(testData3);

            document.getElementById('testUrl1').textContent = testUrl1;
            document.getElementById('testUrl2').textContent = testUrl2;
            document.getElementById('testUrl3').textContent = testUrl3;

            // 设置链接
            document.getElementById('openTest1').href = testUrl1;
            document.getElementById('openTest1').target = '_blank';
            
            document.getElementById('openTest2').href = testUrl2;
            document.getElementById('openTest2').target = '_blank';
            
            document.getElementById('openTest3').href = testUrl3;
            document.getElementById('openTest3').target = '_blank';
        });
    </script>
</body>
</html>
