/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RenderingStates } from "./ui_utils.js";

/**
 * ExternalHighlightManager handles external highlight data passed via URL parameters
 * and renders them on the PDF pages.
 */
class ExternalHighlightManager {
  #highlights = new Map(); // pageNumber -> highlights array
  #pageViews = new Map(); // pageNumber -> PDFPageView
  #enabled = false;

  constructor() {
    this.#parseHighlightsFromURL();

    // Debug logging
    if (this.#enabled) {
      console.log('ExternalHighlightManager: Initialized with', this.#highlights.size, 'pages of highlights');
      for (const [pageNum, highlights] of this.#highlights) {
        console.log(`ExternalHighlightManager: Page ${pageNum} has ${highlights.length} highlights`);
      }
    }
  }

  /**
   * Parse highlight data from URL parameters
   * Expected format: ?highlights=[{"page":10,"x1":100,"y1":200,"x2":400,"y2":250}]
   */
  #parseHighlightsFromURL() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const highlightsParam = urlParams.get('highlights');
      
      if (highlightsParam) {
        const highlightsData = JSON.parse(decodeURIComponent(highlightsParam));
        
        if (Array.isArray(highlightsData)) {
          for (const highlight of highlightsData) {
            if (this.#isValidHighlight(highlight)) {
              const pageNum = highlight.page;
              if (!this.#highlights.has(pageNum)) {
                this.#highlights.set(pageNum, []);
              }
              this.#highlights.get(pageNum).push(highlight);
            }
          }
          this.#enabled = this.#highlights.size > 0;
        }
      }
    } catch (error) {
      console.warn('Failed to parse highlights from URL:', error);
    }
  }

  /**
   * Validate highlight data structure
   */
  #isValidHighlight(highlight) {
    return (
      highlight &&
      typeof highlight.page === 'number' &&
      typeof highlight.x1 === 'number' &&
      typeof highlight.y1 === 'number' &&
      typeof highlight.x2 === 'number' &&
      typeof highlight.y2 === 'number' &&
      highlight.page > 0 &&
      highlight.x1 >= 0 &&
      highlight.y1 >= 0 &&
      highlight.x2 >= 0 &&
      highlight.y2 >= 0
    );
  }

  /**
   * Check if there are highlights for a specific page
   */
  hasHighlightsForPage(pageNumber) {
    return this.#enabled && this.#highlights.has(pageNumber);
  }

  /**
   * Get highlights for a specific page
   */
  getHighlightsForPage(pageNumber) {
    return this.#highlights.get(pageNumber) || [];
  }

  /**
   * Register a page view for highlight rendering
   */
  registerPageView(pageNumber, pageView) {
    console.log(`ExternalHighlightManager: Registering page ${pageNumber}`);
    this.#pageViews.set(pageNumber, pageView);

    // If there are highlights for this page, render them
    if (this.hasHighlightsForPage(pageNumber)) {
      console.log(`ExternalHighlightManager: Found highlights for page ${pageNumber}, rendering...`);
      // Always try to render, the method will handle timing internally
      this.#renderHighlightsForPage(pageNumber);
    } else {
      console.log(`ExternalHighlightManager: No highlights for page ${pageNumber}`);
    }
  }

  /**
   * Unregister a page view
   */
  unregisterPageView(pageNumber) {
    this.#pageViews.delete(pageNumber);
  }

  /**
   * Render highlights for a specific page
   */
  #renderHighlightsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    const highlights = this.getHighlightsForPage(pageNumber);
    
    if (!pageView || !highlights.length) {
      return;
    }

    // Wait for the page to be rendered
    if (pageView.renderingState !== RenderingStates.FINISHED) {
      // Listen for page rendered event
      const onPageRendered = (evt) => {
        if (evt.pageNumber === pageNumber) {
          this.#createHighlightElements(pageView, highlights);
          // Remove the event listener after use
          pageView.eventBus.off('pagerendered', onPageRendered);
        }
      };
      pageView.eventBus.on('pagerendered', onPageRendered);
    } else {
      this.#createHighlightElements(pageView, highlights);
    }
  }

  /**
   * Create highlight DOM elements for a page
   */
  #createHighlightElements(pageView, highlights) {
    const viewport = pageView.viewport;
    const container = pageView.div;
    
    // Remove existing external highlights
    const existingHighlights = container.querySelectorAll('.external-highlight');
    existingHighlights.forEach(el => el.remove());

    // Create highlight layer if it doesn't exist
    let highlightLayer = container.querySelector('.external-highlight-layer');
    if (!highlightLayer) {
      highlightLayer = document.createElement('div');
      highlightLayer.className = 'external-highlight-layer';
      highlightLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 2;
      `;
      container.appendChild(highlightLayer);
    }

    // Create highlight elements
    for (const highlight of highlights) {
      const highlightElement = this.#createHighlightElement(highlight, viewport);
      highlightLayer.appendChild(highlightElement);
    }
  }

  /**
   * Create a single highlight element
   */
  #createHighlightElement(highlight, viewport) {
    const { x1, y1, x2, y2 } = highlight;

    // Convert PDF coordinates to viewport coordinates
    const [viewX1, viewY1] = viewport.convertToViewportPoint(x1, y1);
    const [viewX2, viewY2] = viewport.convertToViewportPoint(x2, y2);

    // Calculate position and dimensions
    const left = Math.min(viewX1, viewX2);
    const top = Math.min(viewY1, viewY2);
    const width = Math.abs(viewX2 - viewX1);
    const height = Math.abs(viewY2 - viewY1);

    // Create highlight container
    const container = document.createElement('div');
    container.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      pointer-events: none;
      z-index: 1;
    `;

    // Create highlight element
    const element = document.createElement('div');

    // Check if highlight has text
    const hasText = highlight.text && highlight.text.trim().length > 0;

    // Build CSS classes
    let className = 'external-highlight';
    if (hasText) {
      className += ' highlight-with-text';
    }
    if (highlight.className) {
      className += ` ${highlight.className}`;
    }
    if (highlight.colorClass) {
      className += ` highlight-${highlight.colorClass}`;
    }
    if (highlight.opacity) {
      if (highlight.opacity <= 0.25) className += ' highlight-light';
      else if (highlight.opacity >= 0.5) className += ' highlight-strong';
      else className += ' highlight-medium';
    }
    if (highlight.borderStyle) {
      className += ` highlight-${highlight.borderStyle}`;
    }
    if (highlight.animation) {
      className += ` highlight-${highlight.animation}`;
    }

    element.className = className;

    // Set basic positioning for highlight element
    element.style.cssText = `
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    `;

    // Apply styles based on whether text is present
    if (hasText) {
      // Apply text highlight styles: light green background with border
      element.style.backgroundColor = '#F6FFED'; // Light green background
      element.style.border = '1px solid #B7EB8F'; // Green border
    } else {
      // Apply default highlight styles
      if (highlight.color && !highlight.colorClass) {
        element.style.backgroundColor = highlight.color;
      }
      if (highlight.borderColor && !highlight.colorClass) {
        element.style.borderColor = highlight.borderColor;
      }
    }

    // Apply other custom styles if provided
    if (highlight.borderWidth) {
      element.style.borderWidth = `${highlight.borderWidth}px`;
    }
    if (highlight.opacity && !highlight.colorClass && !hasText) {
      element.style.opacity = highlight.opacity;
    }
    if (highlight.borderRadius) {
      element.style.borderRadius = `${highlight.borderRadius}px`;
    }

    // Add custom attributes for identification
    element.setAttribute('data-page', highlight.page);
    element.setAttribute('data-highlight-id', highlight.id || `${highlight.page}-${x1}-${y1}-${x2}-${y2}`);

    // Add tooltip if provided
    if (highlight.title || highlight.tooltip) {
      element.setAttribute('title', highlight.title || highlight.tooltip);
      element.style.pointerEvents = 'auto';
    }

    // Add accessibility support
    if (highlight.accessible) {
      element.setAttribute('tabindex', '0');
      element.setAttribute('role', 'mark');
      element.setAttribute('aria-label', highlight.ariaLabel || `Highlight on page ${highlight.page}`);
    }

    // Add highlight element to container
    container.appendChild(element);

    // Create text element if text is provided
    if (hasText) {
      const textElement = this.#createTextElement(highlight.text, width, height);

      // 智能定位文本：优先在高亮区域内显示，空间不足时显示在上方
      const textHeight = this.#estimateTextHeight(highlight.text, width);
      const canFitInside = height >= textHeight + 12; // 12px for padding

      if (canFitInside) {
        // 文本在高亮区域内显示
        textElement.style.cssText += `
          position: absolute;
          left: 4px;
          top: 4px;
          width: calc(100% - 8px);
          max-width: ${width - 8}px;
          pointer-events: none;
          z-index: 2;
        `;
      } else {
        // 文本在高亮区域上方显示
        textElement.style.cssText += `
          position: absolute;
          left: 0;
          bottom: 100%;
          margin-bottom: 2px;
          width: max-content;
          max-width: ${Math.max(width, 200)}px;
          pointer-events: none;
          z-index: 2;
        `;
      }

      container.appendChild(textElement);
    }

    return container;
  }

  /**
   * 估算文本显示所需的高度
   */
  #estimateTextHeight(text, width) {
    if (!text || text.trim().length === 0) {
      return 0;
    }

    const avgCharWidth = 7;
    const lineHeight = 16.8; // 12px font-size * 1.4 line-height
    const padding = 12; // 上下内边距
    const availableWidth = Math.max(width, 200) - 20; // 减去左右内边距
    const charsPerLine = Math.floor(availableWidth / avgCharWidth);
    const estimatedLines = Math.ceil(text.length / charsPerLine);
    const maxLines = 6;

    return Math.min(estimatedLines, maxLines) * lineHeight + padding;
  }

  /**
   * Create a text element for highlight annotation
   */
  #createTextElement(text, highlightWidth, highlightHeight) {
    const textElement = document.createElement('div');
    textElement.className = 'external-highlight-text';

    // 处理长文本：截断和换行
    const processedText = this.#processTextForDisplay(text, highlightWidth);
    textElement.textContent = processedText;

    // 计算文本容器的最大宽度
    const maxWidth = Math.max(highlightWidth, 200);
    const minWidth = Math.min(highlightWidth, 150);

    textElement.style.cssText = `
      background-color: #B7EB8F;
      color: #000000;
      font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
      font-size: 12px;
      padding: 6px 10px;
      border-radius: 4px;
      white-space: normal;
      word-wrap: break-word;
      word-break: break-word;
      line-height: 1.4;
      max-width: ${maxWidth}px;
      min-width: ${minWidth}px;
      max-height: 120px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      border: 1px solid #B7EB8F;
      position: relative;
    `;

    // 如果文本被截断，添加省略号提示
    if (processedText !== text && processedText.length < text.length) {
      const ellipsis = document.createElement('span');
      ellipsis.textContent = '...';
      ellipsis.style.cssText = `
        color: #999;
        font-weight: bold;
      `;
      textElement.appendChild(ellipsis);
    }

    return textElement;
  }

  /**
   * 处理文本以适应显示区域
   */
  #processTextForDisplay(text, highlightWidth) {
    if (!text || text.trim().length === 0) {
      return '';
    }

    // 基于高亮区域宽度估算可显示的字符数
    const avgCharWidth = 7; // 平均字符宽度（像素）
    const padding = 20; // 内边距
    const availableWidth = Math.max(highlightWidth, 200) - padding;
    const maxCharsPerLine = Math.floor(availableWidth / avgCharWidth);
    const maxLines = 6; // 最大行数
    const maxTotalChars = maxCharsPerLine * maxLines;

    let processedText = text.trim();

    // 如果文本过长，进行截断
    if (processedText.length > maxTotalChars) {
      processedText = processedText.substring(0, maxTotalChars - 3);
      // 避免在单词中间截断（对于英文）
      const lastSpace = processedText.lastIndexOf(' ');
      const lastChinese = processedText.search(/[\u4e00-\u9fa5](?![\u4e00-\u9fa5])/);

      if (lastSpace > processedText.length - 10 && lastSpace > 0) {
        processedText = processedText.substring(0, lastSpace);
      } else if (lastChinese > processedText.length - 5 && lastChinese > 0) {
        processedText = processedText.substring(0, lastChinese + 1);
      }
    }

    return processedText;
  }

  /**
   * Update highlights when page is resized or rotated
   */
  updateHighlights(pageNumber) {
    if (this.hasHighlightsForPage(pageNumber)) {
      this.#renderHighlightsForPage(pageNumber);
    }
  }

  /**
   * Check if external highlights are enabled
   */
  get enabled() {
    return this.#enabled;
  }

  /**
   * Get all highlights data
   */
  getAllHighlights() {
    const allHighlights = [];
    for (const [pageNumber, highlights] of this.#highlights) {
      allHighlights.push(...highlights);
    }
    return allHighlights;
  }

  /**
   * Clear all highlights
   */
  clearHighlights() {
    this.#highlights.clear();
    this.#enabled = false;
    
    // Remove highlight elements from all registered pages
    for (const [pageNumber, pageView] of this.#pageViews) {
      const container = pageView.div;
      const highlightLayer = container.querySelector('.external-highlight-layer');
      if (highlightLayer) {
        highlightLayer.remove();
      }
    }
  }

  /**
   * Add new highlights programmatically
   */
  addHighlights(newHighlights) {
    if (!Array.isArray(newHighlights)) {
      return;
    }

    for (const highlight of newHighlights) {
      if (this.#isValidHighlight(highlight)) {
        const pageNum = highlight.page;
        if (!this.#highlights.has(pageNum)) {
          this.#highlights.set(pageNum, []);
        }
        this.#highlights.get(pageNum).push(highlight);
        
        // Render if page is already loaded
        if (this.#pageViews.has(pageNum)) {
          this.#renderHighlightsForPage(pageNum);
        }
      }
    }
    
    this.#enabled = this.#highlights.size > 0;
  }
}

export { ExternalHighlightManager };
