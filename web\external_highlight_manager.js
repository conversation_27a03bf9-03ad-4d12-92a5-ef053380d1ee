/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RenderingStates } from "./ui_utils.js";

/**
 * ExternalHighlightManager handles external highlight data passed via URL parameters
 * and renders them on the PDF pages.
 */
class ExternalHighlightManager {
  #highlights = new Map(); // pageNumber -> highlights array
  #pageViews = new Map(); // pageNumber -> PDFPageView
  #enabled = false;

  constructor() {
    this.#parseHighlightsFromURL();

    // Debug logging
    if (this.#enabled) {
      console.log('ExternalHighlightManager: Initialized with', this.#highlights.size, 'pages of highlights');
      for (const [pageNum, highlights] of this.#highlights) {
        console.log(`ExternalHighlightManager: Page ${pageNum} has ${highlights.length} highlights`);
      }
    }
  }

  /**
   * Parse highlight data from URL parameters
   * Expected format: ?highlights=[{"page":10,"x1":100,"y1":200,"x2":400,"y2":250}]
   */
  #parseHighlightsFromURL() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const highlightsParam = urlParams.get('highlights');
      
      if (highlightsParam) {
        const highlightsData = JSON.parse(decodeURIComponent(highlightsParam));
        
        if (Array.isArray(highlightsData)) {
          for (const highlight of highlightsData) {
            if (this.#isValidHighlight(highlight)) {
              const pageNum = highlight.page;
              if (!this.#highlights.has(pageNum)) {
                this.#highlights.set(pageNum, []);
              }
              this.#highlights.get(pageNum).push(highlight);
            }
          }
          this.#enabled = this.#highlights.size > 0;
        }
      }
    } catch (error) {
      console.warn('Failed to parse highlights from URL:', error);
    }
  }

  /**
   * Validate highlight data structure
   */
  #isValidHighlight(highlight) {
    return (
      highlight &&
      typeof highlight.page === 'number' &&
      typeof highlight.x1 === 'number' &&
      typeof highlight.y1 === 'number' &&
      typeof highlight.x2 === 'number' &&
      typeof highlight.y2 === 'number' &&
      highlight.page > 0 &&
      highlight.x1 >= 0 &&
      highlight.y1 >= 0 &&
      highlight.x2 >= 0 &&
      highlight.y2 >= 0
    );
  }

  /**
   * Check if there are highlights for a specific page
   */
  hasHighlightsForPage(pageNumber) {
    return this.#enabled && this.#highlights.has(pageNumber);
  }

  /**
   * Get highlights for a specific page
   */
  getHighlightsForPage(pageNumber) {
    return this.#highlights.get(pageNumber) || [];
  }

  /**
   * Register a page view for highlight rendering
   */
  registerPageView(pageNumber, pageView) {
    console.log(`ExternalHighlightManager: Registering page ${pageNumber}`);
    this.#pageViews.set(pageNumber, pageView);

    // If there are highlights for this page, render them
    if (this.hasHighlightsForPage(pageNumber)) {
      console.log(`ExternalHighlightManager: Found highlights for page ${pageNumber}, rendering...`);
      // Always try to render, the method will handle timing internally
      this.#renderHighlightsForPage(pageNumber);
    } else {
      console.log(`ExternalHighlightManager: No highlights for page ${pageNumber}`);
    }
  }

  /**
   * Unregister a page view
   */
  unregisterPageView(pageNumber) {
    this.#pageViews.delete(pageNumber);
  }

  /**
   * Render highlights for a specific page
   */
  #renderHighlightsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    const highlights = this.getHighlightsForPage(pageNumber);
    
    if (!pageView || !highlights.length) {
      return;
    }

    // Wait for the page to be rendered
    if (pageView.renderingState !== RenderingStates.FINISHED) {
      // Listen for page rendered event
      const onPageRendered = (evt) => {
        if (evt.pageNumber === pageNumber) {
          this.#createHighlightElements(pageView, highlights);
          // Remove the event listener after use
          pageView.eventBus.off('pagerendered', onPageRendered);
        }
      };
      pageView.eventBus.on('pagerendered', onPageRendered);
    } else {
      this.#createHighlightElements(pageView, highlights);
    }
  }

  /**
   * Create highlight DOM elements for a page
   */
  #createHighlightElements(pageView, highlights) {
    const viewport = pageView.viewport;
    const container = pageView.div;
    
    // Remove existing external highlights
    const existingHighlights = container.querySelectorAll('.external-highlight');
    existingHighlights.forEach(el => el.remove());

    // Create highlight layer if it doesn't exist
    let highlightLayer = container.querySelector('.external-highlight-layer');
    if (!highlightLayer) {
      highlightLayer = document.createElement('div');
      highlightLayer.className = 'external-highlight-layer';
      highlightLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 2;
      `;
      container.appendChild(highlightLayer);
    }

    // Create highlight elements
    for (const highlight of highlights) {
      const highlightElement = this.#createHighlightElement(highlight, viewport);
      highlightLayer.appendChild(highlightElement);
    }
  }

  /**
   * Create a single highlight element
   */
  #createHighlightElement(highlight, viewport) {
    const { x1, y1, x2, y2 } = highlight;

    // Convert PDF coordinates to viewport coordinates
    const [viewX1, viewY1] = viewport.convertToViewportPoint(x1, y1);
    const [viewX2, viewY2] = viewport.convertToViewportPoint(x2, y2);

    // Calculate position and dimensions
    const left = Math.min(viewX1, viewX2);
    const top = Math.min(viewY1, viewY2);
    const width = Math.abs(viewX2 - viewX1);
    const height = Math.abs(viewY2 - viewY1);

    // Create highlight container
    const container = document.createElement('div');
    container.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      pointer-events: none;
      z-index: 1;
    `;

    // Create highlight element
    const element = document.createElement('div');

    // Check if highlight has text
    const hasText = highlight.text && highlight.text.trim().length > 0;

    // Build CSS classes
    let className = 'external-highlight';
    if (hasText) {
      className += ' highlight-with-text';
    }
    if (highlight.className) {
      className += ` ${highlight.className}`;
    }
    if (highlight.colorClass) {
      className += ` highlight-${highlight.colorClass}`;
    }
    if (highlight.opacity) {
      if (highlight.opacity <= 0.25) className += ' highlight-light';
      else if (highlight.opacity >= 0.5) className += ' highlight-strong';
      else className += ' highlight-medium';
    }
    if (highlight.borderStyle) {
      className += ` highlight-${highlight.borderStyle}`;
    }
    if (highlight.animation) {
      className += ` highlight-${highlight.animation}`;
    }

    element.className = className;

    // Set basic positioning for highlight element
    element.style.cssText = `
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    `;

    // Apply styles based on whether text is present
    if (hasText) {
      // Apply text highlight styles: opaque light green background, black text
      element.style.backgroundColor = 'rgba(144, 238, 144, 1)'; // Light green, fully opaque
      element.style.border = '1px solid rgba(144, 238, 144, 1)';
    } else {
      // Apply default highlight styles
      if (highlight.color && !highlight.colorClass) {
        element.style.backgroundColor = highlight.color;
      }
      if (highlight.borderColor && !highlight.colorClass) {
        element.style.borderColor = highlight.borderColor;
      }
    }

    // Apply other custom styles if provided
    if (highlight.borderWidth) {
      element.style.borderWidth = `${highlight.borderWidth}px`;
    }
    if (highlight.opacity && !highlight.colorClass && !hasText) {
      element.style.opacity = highlight.opacity;
    }
    if (highlight.borderRadius) {
      element.style.borderRadius = `${highlight.borderRadius}px`;
    }

    // Add custom attributes for identification
    element.setAttribute('data-page', highlight.page);
    element.setAttribute('data-highlight-id', highlight.id || `${highlight.page}-${x1}-${y1}-${x2}-${y2}`);

    // Add tooltip if provided
    if (highlight.title || highlight.tooltip) {
      element.setAttribute('title', highlight.title || highlight.tooltip);
      element.style.pointerEvents = 'auto';
    }

    // Add accessibility support
    if (highlight.accessible) {
      element.setAttribute('tabindex', '0');
      element.setAttribute('role', 'mark');
      element.setAttribute('aria-label', highlight.ariaLabel || `Highlight on page ${highlight.page}`);
    }

    // Add highlight element to container
    container.appendChild(element);

    // Create text element if text is provided
    if (hasText) {
      const textElement = this.#createTextElement(highlight.text, width);
      // Position text above the highlight
      textElement.style.cssText = `
        position: absolute;
        left: 0;
        bottom: 100%;
        margin-bottom: 2px;
        width: max-content;
        max-width: ${Math.max(width, 200)}px;
        pointer-events: none;
        z-index: 2;
      `;
      container.appendChild(textElement);
    }

    return container;
  }

  /**
   * Create a text element for highlight annotation
   */
  #createTextElement(text, highlightWidth) {
    const textElement = document.createElement('div');
    textElement.className = 'external-highlight-text';
    textElement.textContent = text;

    textElement.style.cssText = `
      background-color: rgba(144, 238, 144, 1);
      color: #000000;
      font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid rgba(144, 238, 144, 1);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    `;

    return textElement;
  }

  /**
   * Update highlights when page is resized or rotated
   */
  updateHighlights(pageNumber) {
    if (this.hasHighlightsForPage(pageNumber)) {
      this.#renderHighlightsForPage(pageNumber);
    }
  }

  /**
   * Check if external highlights are enabled
   */
  get enabled() {
    return this.#enabled;
  }

  /**
   * Get all highlights data
   */
  getAllHighlights() {
    const allHighlights = [];
    for (const [pageNumber, highlights] of this.#highlights) {
      allHighlights.push(...highlights);
    }
    return allHighlights;
  }

  /**
   * Clear all highlights
   */
  clearHighlights() {
    this.#highlights.clear();
    this.#enabled = false;
    
    // Remove highlight elements from all registered pages
    for (const [pageNumber, pageView] of this.#pageViews) {
      const container = pageView.div;
      const highlightLayer = container.querySelector('.external-highlight-layer');
      if (highlightLayer) {
        highlightLayer.remove();
      }
    }
  }

  /**
   * Add new highlights programmatically
   */
  addHighlights(newHighlights) {
    if (!Array.isArray(newHighlights)) {
      return;
    }

    for (const highlight of newHighlights) {
      if (this.#isValidHighlight(highlight)) {
        const pageNum = highlight.page;
        if (!this.#highlights.has(pageNum)) {
          this.#highlights.set(pageNum, []);
        }
        this.#highlights.get(pageNum).push(highlight);
        
        // Render if page is already loaded
        if (this.#pageViews.has(pageNum)) {
          this.#renderHighlightsForPage(pageNum);
        }
      }
    }
    
    this.#enabled = this.#highlights.size > 0;
  }
}

export { ExternalHighlightManager };
