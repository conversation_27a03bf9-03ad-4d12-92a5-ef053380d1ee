/**
 * 测试高亮文本功能的示例代码
 */

// 测试数据：包含带文本和不带文本的高亮
const testHighlights = [
  // 带文本的高亮 - 应该显示浅绿色背景和文本
  {
    page: 1,
    x1: 100,
    y1: 200,
    x2: 400,
    y2: 250,
    text: "这是一个重要的注释",
    id: "highlight-with-text-1"
  },
  
  // 不带文本的高亮 - 应该使用默认样式
  {
    page: 1,
    x1: 100,
    y1: 300,
    x2: 400,
    y2: 350,
    id: "highlight-default-1"
  },
  
  // 带长文本的高亮 - 测试文本截断
  {
    page: 1,
    x1: 100,
    y1: 400,
    x2: 400,
    y2: 450,
    text: "这是一个非常长的文本注释，用来测试文本截断功能是否正常工作",
    id: "highlight-long-text-1"
  },
  
  // 带空文本的高亮 - 应该使用默认样式
  {
    page: 1,
    x1: 100,
    y1: 500,
    x2: 400,
    y2: 550,
    text: "",
    id: "highlight-empty-text-1"
  },
  
  // 带空格文本的高亮 - 应该使用默认样式
  {
    page: 1,
    x1: 100,
    y1: 600,
    x2: 400,
    y2: 650,
    text: "   ",
    id: "highlight-whitespace-text-1"
  }
];

/**
 * 测试函数：验证高亮文本功能
 */
function testHighlightTextFeature() {
  console.log('开始测试高亮文本功能...');
  
  // 检查ExternalHighlightManager是否存在
  if (typeof ExternalHighlightManager === 'undefined') {
    console.error('ExternalHighlightManager 未定义');
    return false;
  }
  
  try {
    // 创建高亮管理器实例
    const highlightManager = new ExternalHighlightManager();
    
    // 添加测试高亮
    highlightManager.addHighlights(testHighlights);
    
    console.log('测试高亮已添加:', testHighlights.length, '个');
    
    // 验证高亮是否正确添加
    for (const highlight of testHighlights) {
      const hasHighlights = highlightManager.hasHighlightsForPage(highlight.page);
      console.log(`页面 ${highlight.page} 是否有高亮:`, hasHighlights);
    }
    
    console.log('高亮文本功能测试完成');
    return true;
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    return false;
  }
}

/**
 * 生成测试URL
 */
function generateTestURL(baseURL = 'viewer.html') {
  const highlightsParam = encodeURIComponent(JSON.stringify(testHighlights));
  return `${baseURL}?highlights=${highlightsParam}`;
}

/**
 * 验证高亮元素是否正确创建
 */
function validateHighlightElements() {
  console.log('验证高亮元素...');
  
  const highlightElements = document.querySelectorAll('.external-highlight');
  const textElements = document.querySelectorAll('.external-highlight-text');
  
  console.log('找到高亮元素:', highlightElements.length);
  console.log('找到文本元素:', textElements.length);
  
  // 验证带文本的高亮
  const textHighlights = document.querySelectorAll('.external-highlight.highlight-with-text');
  console.log('带文本的高亮元素:', textHighlights.length);
  
  // 验证样式
  textHighlights.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element);
    console.log(`文本高亮 ${index + 1} 样式:`, {
      backgroundColor: computedStyle.backgroundColor,
      border: computedStyle.border,
      opacity: computedStyle.opacity
    });
  });
  
  textElements.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element);
    console.log(`文本元素 ${index + 1} 样式:`, {
      backgroundColor: computedStyle.backgroundColor,
      color: computedStyle.color,
      fontFamily: computedStyle.fontFamily,
      fontSize: computedStyle.fontSize
    });
  });
}

/**
 * 在页面加载完成后运行测试
 */
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    console.log('页面加载完成，开始测试...');
    
    // 延迟执行，确保PDF.js完全加载
    setTimeout(() => {
      testHighlightTextFeature();
      
      // 再次延迟验证元素
      setTimeout(validateHighlightElements, 2000);
    }, 1000);
  });
  
  // 导出到全局作用域以便手动测试
  window.testHighlightTextFeature = testHighlightTextFeature;
  window.generateTestURL = generateTestURL;
  window.validateHighlightElements = validateHighlightElements;
  window.testHighlights = testHighlights;
}

// 如果在Node.js环境中
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testHighlightTextFeature,
    generateTestURL,
    validateHighlightElements,
    testHighlights
  };
}

console.log('高亮文本测试脚本已加载');
console.log('测试URL示例:', generateTestURL());
