# PDF.js 高亮文本功能

基于PDF.js的外部高亮选区功能，新增了文本显示功能。当高亮数据包含文本时，会在选区上方显示文本，并使用特殊的样式。

## 功能特性

### 🎨 视觉效果
- **有文本时**：不透明的浅绿色背景 `rgba(144, 238, 144, 1)`
- **无文本时**：保持原有的默认高亮样式
- **文本样式**：黑色微软雅黑字体，12px大小
- **文本位置**：显示在高亮选区上方

### 🔧 技术特性
- 完全向后兼容现有高亮功能
- 支持响应式设计
- 支持深色模式、高对比度模式
- 支持打印样式
- 文本超长时自动截断显示

## 使用方法

### 1. URL参数方式

在PDF.js的URL中添加highlights参数，包含text字段：

```javascript
// 带文本的高亮
?highlights=[
  {
    "page": 1,
    "x1": 100,
    "y1": 200,
    "x2": 400,
    "y2": 250,
    "text": "这是高亮文本注释"
  }
]

// 不带文本的高亮（使用默认样式）
?highlights=[
  {
    "page": 1,
    "x1": 100,
    "y1": 300,
    "x2": 400,
    "y2": 350
  }
]
```

### 2. JavaScript API方式

```javascript
// 创建外部高亮管理器实例
const highlightManager = new ExternalHighlightManager();

// 添加带文本的高亮
highlightManager.addHighlights([
  {
    page: 1,
    x1: 100,
    y1: 200,
    x2: 400,
    y2: 250,
    text: "重要内容标注"
  },
  {
    page: 1,
    x1: 100,
    y1: 300,
    x2: 400,
    y2: 350
    // 没有text字段，使用默认样式
  }
]);
```

## 数据格式

### 高亮对象属性

| 属性 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `page` | number | ✅ | 页码（从1开始） |
| `x1` | number | ✅ | 左上角X坐标 |
| `y1` | number | ✅ | 左上角Y坐标 |
| `x2` | number | ✅ | 右下角X坐标 |
| `y2` | number | ✅ | 右下角Y坐标 |
| `text` | string | ❌ | 要显示的文本内容 |
| `id` | string | ❌ | 高亮的唯一标识符 |

### 文本处理规则

- 如果 `text` 字段存在且不为空（去除空格后），则显示文本并应用特殊样式
- 如果 `text` 字段不存在、为空或只包含空格，则使用默认高亮样式
- 超长文本会自动截断并显示省略号

## 样式定制

### CSS类名

- `.external-highlight` - 基础高亮样式
- `.external-highlight.highlight-with-text` - 带文本的高亮样式
- `.external-highlight-text` - 文本元素样式

### 自定义样式示例

```css
/* 自定义文本高亮背景色 */
.external-highlight.highlight-with-text {
  background-color: rgba(255, 182, 193, 1) !important; /* 浅粉色 */
  border: 1px solid rgba(255, 182, 193, 1) !important;
}

/* 自定义文本样式 */
.external-highlight-text {
  background-color: rgba(255, 182, 193, 1);
  color: #8B0000; /* 深红色文字 */
  font-size: 14px;
  font-weight: bold;
}
```

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 响应式支持
- 移动端设备
- 平板设备
- 桌面设备

### 辅助功能
- 支持高对比度模式
- 支持深色模式
- 支持打印样式

## 测试

### 运行测试
```javascript
// 在浏览器控制台中运行
testHighlightTextFeature();

// 验证元素是否正确创建
validateHighlightElements();

// 生成测试URL
console.log(generateTestURL());
```

### 测试文件
- `test_highlight_text.js` - 功能测试脚本
- `highlight_text_example.html` - 视觉效果演示

## 更新日志

### v1.0.0
- ✨ 新增文本显示功能
- 🎨 添加特殊的文本高亮样式
- 📱 支持响应式设计
- 🌙 支持深色模式
- 🖨️ 支持打印样式
- ♿ 支持辅助功能

## 注意事项

1. **性能考虑**：大量高亮时建议分批加载
2. **文本长度**：建议文本长度不超过50个字符
3. **坐标系统**：使用PDF坐标系统（左下角为原点）
4. **字体回退**：如果系统没有微软雅黑，会自动回退到sans-serif
5. **样式优先级**：文本高亮样式使用`!important`确保正确显示

## 故障排除

### 常见问题

**Q: 文本没有显示？**
A: 检查text字段是否存在且不为空，确保去除空格后有内容。

**Q: 样式不正确？**
A: 确保CSS文件已正确加载，检查是否有其他样式覆盖。

**Q: 在移动端显示异常？**
A: 检查viewport设置，确保响应式样式生效。

**Q: 打印时样式丢失？**
A: 确保浏览器支持打印颜色，或在打印设置中启用背景图形。
