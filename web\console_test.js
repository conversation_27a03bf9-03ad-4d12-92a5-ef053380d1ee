/**
 * 浏览器控制台测试脚本
 * 在PDF.js viewer页面的控制台中运行此脚本来测试高亮文本功能
 */

// 测试高亮数据
const testHighlights = [
    // 带文本的高亮 - 电化学测试仪相关
    {
        page: 1,
        x1: 100,
        y1: 700,
        x2: 300,
        y2: 720,
        text: "电化学测试仪",
        id: "electrochemical-tester"
    },
    
    // 带文本的高亮 - 采购信息
    {
        page: 1,
        x1: 100,
        y1: 650,
        x2: 250,
        y2: 670,
        text: "采购信息",
        id: "procurement-info"
    },
    
    // 带文本的高亮 - 深圳大学
    {
        page: 1,
        x1: 50,
        y1: 750,
        x2: 200,
        y2: 770,
        text: "深圳大学",
        id: "shenzhen-university"
    },
    
    // 不带文本的高亮 - 默认样式
    {
        page: 1,
        x1: 100,
        y1: 600,
        x2: 200,
        y2: 620,
        id: "default-highlight"
    },
    
    // 长文本测试
    {
        page: 1,
        x1: 100,
        y1: 550,
        x2: 400,
        y2: 570,
        text: "这是一个很长的文本注释，用来测试文本截断功能",
        id: "long-text-test"
    }
];

/**
 * 主测试函数
 */
function runHighlightTextTest() {
    console.log('🚀 开始测试PDF.js高亮文本功能...');
    
    try {
        // 检查是否在PDF.js环境中
        if (typeof PDFViewerApplication === 'undefined') {
            console.error('❌ 错误：请在PDF.js viewer页面中运行此脚本');
            return false;
        }
        
        // 检查ExternalHighlightManager是否存在
        if (typeof ExternalHighlightManager === 'undefined') {
            console.error('❌ 错误：ExternalHighlightManager未定义，请确保相关文件已加载');
            return false;
        }
        
        console.log('✅ PDF.js环境检查通过');
        
        // 创建高亮管理器
        const highlightManager = new ExternalHighlightManager();
        console.log('✅ ExternalHighlightManager创建成功');
        
        // 添加测试高亮
        highlightManager.addHighlights(testHighlights);
        console.log('✅ 测试高亮已添加:', testHighlights.length, '个');
        
        // 验证高亮数据
        testHighlights.forEach((highlight, index) => {
            const hasHighlights = highlightManager.hasHighlightsForPage(highlight.page);
            console.log(`📄 页面 ${highlight.page} 高亮检查:`, hasHighlights ? '✅' : '❌');
        });
        
        // 延迟验证DOM元素
        setTimeout(() => {
            validateHighlightElements();
        }, 2000);
        
        console.log('🎉 高亮文本功能测试完成！');
        return true;
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        return false;
    }
}

/**
 * 验证高亮DOM元素
 */
function validateHighlightElements() {
    console.log('🔍 验证高亮DOM元素...');
    
    const highlightElements = document.querySelectorAll('.external-highlight');
    const textElements = document.querySelectorAll('.external-highlight-text');
    const textHighlights = document.querySelectorAll('.external-highlight.highlight-with-text');
    
    console.log('📊 元素统计:');
    console.log('  - 总高亮元素:', highlightElements.length);
    console.log('  - 文本元素:', textElements.length);
    console.log('  - 带文本高亮:', textHighlights.length);
    
    // 验证带文本的高亮样式
    if (textHighlights.length > 0) {
        console.log('🎨 验证文本高亮样式:');
        textHighlights.forEach((element, index) => {
            const style = window.getComputedStyle(element);
            const bgColor = style.backgroundColor;
            const opacity = style.opacity;
            
            console.log(`  高亮 ${index + 1}:`, {
                backgroundColor: bgColor,
                opacity: opacity,
                isCorrectColor: bgColor.includes('144, 238, 144') || bgColor.includes('lightgreen')
            });
        });
    }
    
    // 验证文本元素样式
    if (textElements.length > 0) {
        console.log('📝 验证文本元素样式:');
        textElements.forEach((element, index) => {
            const style = window.getComputedStyle(element);
            const color = style.color;
            const fontFamily = style.fontFamily;
            const fontSize = style.fontSize;
            
            console.log(`  文本 ${index + 1}:`, {
                color: color,
                fontFamily: fontFamily,
                fontSize: fontSize,
                content: element.textContent
            });
        });
    }
    
    // 检查是否有错误
    if (highlightElements.length === 0) {
        console.warn('⚠️ 警告：没有找到高亮元素，可能需要等待PDF加载完成');
    } else {
        console.log('✅ DOM元素验证完成');
    }
}

/**
 * 清除所有测试高亮
 */
function clearTestHighlights() {
    console.log('🧹 清除测试高亮...');
    
    const highlightLayers = document.querySelectorAll('.external-highlight-layer');
    highlightLayers.forEach(layer => {
        layer.remove();
    });
    
    console.log('✅ 测试高亮已清除');
}

/**
 * 生成测试URL
 */
function generateTestURL() {
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('?')[0];
    const highlightsParam = encodeURIComponent(JSON.stringify(testHighlights));
    
    // 保留现有的file参数
    const urlParams = new URLSearchParams(window.location.search);
    const fileParam = urlParams.get('file');
    
    let testUrl = `${baseUrl}?highlights=${highlightsParam}`;
    if (fileParam) {
        testUrl += `&file=${encodeURIComponent(fileParam)}`;
    }
    
    console.log('🔗 测试URL:', testUrl);
    return testUrl;
}

/**
 * 手动添加单个高亮进行测试
 */
function addSingleHighlight(page, x1, y1, x2, y2, text = null) {
    try {
        if (typeof ExternalHighlightManager === 'undefined') {
            console.error('❌ ExternalHighlightManager未定义');
            return;
        }
        
        const highlight = {
            page: page,
            x1: x1,
            y1: y1,
            x2: x2,
            y2: y2,
            id: `manual-${Date.now()}`
        };
        
        if (text) {
            highlight.text = text;
        }
        
        const manager = new ExternalHighlightManager();
        manager.addHighlights([highlight]);
        
        console.log('✅ 手动高亮已添加:', highlight);
        
    } catch (error) {
        console.error('❌ 添加高亮失败:', error);
    }
}

// 导出到全局作用域
window.runHighlightTextTest = runHighlightTextTest;
window.validateHighlightElements = validateHighlightElements;
window.clearTestHighlights = clearTestHighlights;
window.generateTestURL = generateTestURL;
window.addSingleHighlight = addSingleHighlight;
window.testHighlights = testHighlights;

// 显示使用说明
console.log(`
🎯 PDF.js 高亮文本功能测试脚本已加载

📋 可用命令:
  runHighlightTextTest()     - 运行完整测试
  validateHighlightElements() - 验证DOM元素
  clearTestHighlights()      - 清除测试高亮
  generateTestURL()          - 生成测试URL
  addSingleHighlight(page, x1, y1, x2, y2, text) - 手动添加高亮

🚀 快速开始:
  1. 运行: runHighlightTextTest()
  2. 等待2秒后查看验证结果
  3. 如需清除: clearTestHighlights()

💡 示例:
  addSingleHighlight(1, 100, 500, 300, 520, "测试文本")
`);

// 自动检查环境
if (typeof PDFViewerApplication !== 'undefined') {
    console.log('✅ 检测到PDF.js环境，可以开始测试');
} else {
    console.warn('⚠️ 未检测到PDF.js环境，请在PDF viewer页面中运行');
}
